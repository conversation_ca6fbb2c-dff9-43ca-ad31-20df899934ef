//
//  AppDelegate.swift
//  Merit
//
//  Created by <PERSON><PERSON> on 4/23/67.
//
import CUIModule
import APILayer
import Storage
import IQKeyboardManagerSwift
import RxSwift
import UIKit
import Kingfisher
import Wormholy
import SharedData

class AppDelegate: UIResponder, UIApplicationDelegate {
    private let disposeBag = DisposeBag()

    private lazy var mainWindow = UIWindow()
    private let router = AppCoordinator().strongRouter

    private var orientationLock = ScreenOrientation.defaultOrientation

    // JPush configuration
    static var jpushAppKey: String = ""
    static var jpushForProd: Bool = false

    // Article link handling
    private var pendingArticleURL: String?
    private var appStartTime: Date?

    func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        // Override point for customization after application launch.
        appStartTime = Date() // Track app start time for notification handling
        configureAppS<PERSON>tService()
        mainWindow.set(AppTheme.init(rawValue: LocalPreference.appTheme ?? AppTheme.default.rawValue) ?? .default)
        configureURLEnvironment()
        observeSessionTimeout()
        setUpPushNotification(launchOptions: launchOptions)
        router.setRoot(for: mainWindow)
        
        CustomThemeManager.shared.saveTheme(
            CustomThemeModel(
                name: "", primaryId: ThemeColor.presetPrimary1.rawValue,
                secondaryId: ThemeColor.presetSecondary1.rawValue,
                thirdId: ThemeColor.presetThird1.rawValue,
                textHighLightId: ThemeColor.presetTextHighLight1.rawValue,
                isPreset: true, isEnable: true
            )
        )
        
        ScreenOrientation.setOrientation
            .subscribe(onNext: { [unowned self] in
                orientationLock = $0
                
                if orientationLock == .landscapeRight {
                    UIDevice.current.setValue(UIInterfaceOrientation.landscapeRight.rawValue, forKey: "orientation")
                }
            }).disposed(by: disposeBag)

        return true
    }
    
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        orientationLock
    }

    func application(
        _ application: UIApplication,
        shouldAllowExtensionPointIdentifier extensionPointIdentifier: UIApplication.ExtensionPointIdentifier
    ) -> Bool {
        return extensionPointIdentifier != UIApplication.ExtensionPointIdentifier.keyboard
    }
    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        JPUSHService.registerDeviceToken(deviceToken)
    }
    
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: any Error) {
        print("Did failed to register For Remote Notifications With Error: \(error.localizedDescription)")
    }
    
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        JPUSHService.handleRemoteNotification(userInfo)
        completionHandler(.newData)
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
        // Sync badge count with local notification state when app becomes active
        BadgeCountManager.shared.syncBadgeCountWithLocalState()

        // Handle pending article URL when app becomes active (from background)
        if let pendingURL = pendingArticleURL {
            pendingArticleURL = nil
            // Delay to ensure the app is fully active and UI is ready
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                self?.openArticleInApp(url: pendingURL)
            }
        }
    }

    func applicationDidEnterBackground(_ application: UIApplication) {
        // Sync badge count when app enters background to ensure consistency
        BadgeCountManager.shared.syncBadgeCountWithLocalState()
    }

    func applicationWillTerminate(_ application: UIApplication) {
        // Final sync before app terminates
        BadgeCountManager.shared.syncBadgeCountWithLocalState()
    }
}

// MARK: - App Configuration
extension AppDelegate {

    func configureAppStartService() {
        configureAppTarget()
        UIFont.loadAllFonts()
        configureKeyboard()
        setImageCachePolicy()
        setDeviceID()
    }

    func configureAppTarget() {
    #if DEV
    LocalPreference.appTarget = AppTarget.dev.rawValue
    // Enable Wormholy shake gesture in DEV for network debugging
    Wormholy.shakeEnabled = true
    print("🐛 [DEBUG] Wormholy enabled (DEV) — shake device to open network inspector")
    AppDelegate.jpushAppKey = "d733ba82d878ba46bc9ebf5a"
    AppDelegate.jpushForProd = false
    #elseif UAT
    LocalPreference.appTarget = AppTarget.uat.rawValue
    // Enable Wormholy shake gesture in UAT for network debugging
    Wormholy.shakeEnabled = true
    print("🐛 [DEBUG] Wormholy enabled (UAT) — shake device to open network inspector")
    AppDelegate.jpushAppKey = "d733ba82d878ba46bc9ebf5a"
    AppDelegate.jpushForProd = false
    #elseif PROD
    LocalPreference.appTarget = AppTarget.prod.rawValue
    // Keep Wormholy disabled in PROD
    Wormholy.shakeEnabled = false
    AppDelegate.jpushAppKey = "d733ba82d878ba46bc9ebf5a"
    AppDelegate.jpushForProd = true
    #else
    LocalPreference.appTarget = AppTarget.uat.rawValue
    // Default to disabled unless explicitly DEV/UAT
    Wormholy.shakeEnabled = false
    AppDelegate.jpushForProd = false
    #endif
    }

    func configureKeyboard() {
        IQKeyboardManager.shared.enable = true
        IQKeyboardManager.shared.shouldResignOnTouchOutside = true
        IQKeyboardManager.shared.shouldShowToolbarPlaceholder = false
    }

    func configureURLEnvironment() {
        switch AppTarget.get(LocalPreference.appTarget) {
        case .dev:
            APIConstants.shared.configure(environment: .dev)
            WSConfiguration.config(env: .dev)

        case .uat:
            APIConstants.shared.configure(environment: .uat)
            WSConfiguration.config(env: .uat)

        case .prod:
            APIConstants.shared.configure(environment: .prod)
            WSConfiguration.config(env: .prod)
        }

        // Set custom URL for address info endpoint
        let addressEnvironment = URLEnvironment(url: "https://aig-wallet-sit.siriustech.io",
                                                name: "Address Info",
                                                version: "0.0.1",
                                                enableSSLPinning: true,
                                                allowInsecureConnection: true)

        APIConstants.shared.registerCustomUrlFor(endpoint: AddressInfoEndPoint.service,
                                                 url: addressEnvironment)
        // Set custom URL for address info endpoint
        let newsEnvironment = URLEnvironment(url: "https://api.marketaux.com",
                                             name: "News",
                                             version: "0.0.1",
                                             enableSSLPinning: true,
                                             allowInsecureConnection: true)

        APIConstants.shared.registerCustomUrlFor(endpoint: GetMarketNewsEndPoint.service,
                                                 url: newsEnvironment)
    }
    
    func setImageCachePolicy() {
        ImageCache.default.memoryStorage.config.expiration = .days(7)
    }
    
    func setDeviceID() {
        if LocalPreference.deviceId == nil {
            LocalPreference.deviceId = UIDevice.current.identifierForVendor?.uuidString
        }
        
        LocalPreference.appVersion = "\(Bundle.main.releaseVersionNumber ?? "").\(Bundle.main.buildVersionNumber ?? "")"
    }
    
    func observeSessionTimeout() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(self.sessionTimeout(_:)),
                                               name: .sessionTimeout,
                                               object: nil)
    }
    
    @objc func sessionTimeout(_ notification: Notification) {
        router.trigger(.logout(isSessionExpired: true))
    }
    
    func setUpPushNotification(launchOptions: [UIApplication.LaunchOptionsKey: Any]?) {
        // check push noti
        SystemAuthorityHandler.checkPushNotiAuth(handler: { _ in
            // can do more if not authroize
            // eg: like show alert to allow noti in setting
        })
        UNUserNotificationCenter.current().delegate = self
        
        let entity = JPUSHRegisterEntity()
        entity.types = NSInteger(UNAuthorizationOptions.alert.rawValue) |
        NSInteger(UNAuthorizationOptions.sound.rawValue) |
        NSInteger(UNAuthorizationOptions.badge.rawValue) |
        NSInteger(UNAuthorizationOptions.provisional.rawValue)
        
        JPUSHService.register(forRemoteNotificationConfig: entity, delegate: self)
        
        JPUSHService.registrationIDCompletionHandler { _, registrationID in
            Keychain.jPushRegistrationID = registrationID
        }
        
        JPUSHService.setup(withOption: launchOptions,
                           appKey: AppDelegate.jpushAppKey,
                           channel: "",
                           apsForProduction: AppDelegate.jpushForProd)
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension AppDelegate: UNUserNotificationCenterDelegate {

    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        if #available(iOS 14.0, *) {
            completionHandler([.sound, .list, .banner])
        } else {
            completionHandler([.sound, .alert])
        }
    }
}

// MARK: - JPUSHRegisterDelegate
extension AppDelegate: JPUSHRegisterDelegate {
    
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (Int) -> Void) {
        let userInfo = notification.request.content.userInfo
        let request = notification.request // 收到推送的请求
        //        let content = request.content // 收到推送的消息内容
        
        //        let badge = content.badge // 推送消息的角标
        //        let body = content.body   // 推送消息体
        //        let sound = content.sound // 推送消息的声音
        //        let subtitle = content.subtitle // 推送消息的副标题
        //        let title = content.title // 推送消息的标题
        
        if (notification.request.trigger?.isKind(of: UNPushNotificationTrigger.self) == true) {
            // 注意调用
            JPUSHService.handleRemoteNotification(userInfo)
        } else {
            
        }
        
        completionHandler(Int(UNNotificationPresentationOptions.badge.rawValue |
                              UNNotificationPresentationOptions.sound.rawValue |
                              UNNotificationPresentationOptions.alert.rawValue))
    }
    
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        let request = response.notification.request // 收到推送的请求
        //        let content = request.content // 收到推送的消息内容

        //        let badge = content.badge // 推送消息的角标
        //        let body = content.body   // 推送消息体
        //        let sound = content.sound // 推送消息的声音
        //        let subtitle = content.subtitle // 推送消息的副标题
        //        let title = content.title // 推送消息的标题

        if (response.notification.request.trigger?.isKind(of: UNPushNotificationTrigger.self) == true) {
            // 注意调用
            JPUSHService.handleRemoteNotification(userInfo)

            // Decrement badge count when user taps on notification
            BadgeCountManager.shared.decrementBadgeCount()

            // Handle article link notifications
            handleArticleLinkNotification(userInfo: userInfo)

        } else {

        }

        completionHandler()
    }
    
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, openSettingsFor notification: UNNotification) {
        
    }
    
    func jpushNotificationAuthorization(_ status: JPAuthorizationStatus, withInfo info: [AnyHashable : Any]?) {

    }

    // MARK: - Article Link Notification Handling
    private func handleArticleLinkNotification(userInfo: [AnyHashable: Any]) {
        // Extract article URL from notification payload
        guard let articleURL = extractArticleURL(from: userInfo) else {
            return
        }

        // Navigate to article page
        navigateToArticle(url: articleURL)
    }

    private func extractArticleURL(from userInfo: [AnyHashable: Any]) -> String? {
        // Try different possible keys for extras
        var extras: [String: Any]?

        // Method 1: Direct "extras" key
        if let directExtras = userInfo["extras"] as? [String: Any] {
            extras = directExtras
        }
        // Method 2: Try with NSString key (common JPush issue)
        else if let nsStringExtras = userInfo[NSString(string: "extras")] as? [String: Any] {
            extras = nsStringExtras
        }
        // Method 3: Check if it's nested in JPush specific keys
        else if let jpushExtras = userInfo["_j_extras"] as? [String: Any] {
            extras = jpushExtras
        }
        // Method 4: Check for custom data in different JPush formats
        else if let customData = userInfo["custom"] as? [String: Any] {
            extras = customData
        }
        // Method 5: Check if extras is a string that needs parsing
        else if let extrasString = userInfo["extras"] as? String {
            if let data = extrasString.data(using: .utf8),
               let jsonExtras = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                extras = jsonExtras
            }
        }

        guard let finalExtras = extras else {
            return nil
        }

        // Check if this is an article message type
        guard let messageType = finalExtras["message_type"] as? String,
              messageType == "ARTICLE" else {
            return nil
        }

        // Extract article URL
        return finalExtras["article_url"] as? String
    }

    private func extractURLFromText(_ text: String) -> String? {
        // Use regex to find HTTP/HTTPS URLs in the text
        let urlPattern = "https?://[^\\s]+"
        let regex = try? NSRegularExpression(pattern: urlPattern, options: .caseInsensitive)
        let range = NSRange(location: 0, length: text.utf16.count)

        if let match = regex?.firstMatch(in: text, options: [], range: range) {
            let urlRange = Range(match.range, in: text)
            return urlRange.map { String(text[$0]) }
        }

        return nil
    }

    private func navigateToArticle(url: String) {
        // Ensure we're on the main thread
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            let appState = UIApplication.shared.applicationState

            // Check if app was launched from terminated state
            let wasAppLaunchedFromTerminated = self.wasLaunchedFromTerminatedState()

            switch appState {
            case .active:
                // Case 1: App is in foreground - navigate immediately
                self.openArticleInApp(url: url)

            case .background:
                // Case 2: App is in background - store URL and navigate when app becomes active
                self.pendingArticleURL = url

            case .inactive:
                if wasAppLaunchedFromTerminated {
                    // Case 3: App was completely closed - store URL for after login
                    self.storePendingArticleForLogin(url: url)
                } else {
                    // Case 4: App is transitioning from background - store URL temporarily
                    self.pendingArticleURL = url
                }

            @unknown default:
                // Fallback - store URL for later
                self.pendingArticleURL = url
            }
        }
    }

    private func openArticleInApp(url: String) {
        // Post a notification with the article URL
        // This allows any part of the app to listen and handle the navigation
        NotificationCenter.default.post(
            name: .openArticleFromNotification,
            object: nil,
            userInfo: ["articleURL": url]
        )
    }

    private func storePendingArticleForLogin(url: String) {
        UserDefaults.standard.set(url, forKey: "pendingArticleAfterLogin")
        UserDefaults.standard.synchronize()
    }

    // Method to be called after successful login
    func handlePendingArticleAfterLogin() {
        if let pendingURL = UserDefaults.standard.string(forKey: "pendingArticleAfterLogin") {
            // Clear the stored URL
            UserDefaults.standard.removeObject(forKey: "pendingArticleAfterLogin")
            UserDefaults.standard.synchronize()

            // Navigate to the article
            openArticleInApp(url: pendingURL)
        }
    }

    private func wasLaunchedFromTerminatedState() -> Bool {
        // Check if this is the first time the app delegate methods are being called
        // If the app was just backgrounded and brought back, certain properties would still be set

        // Method 1: Check if we have any existing pending URL (indicates app was running)
        if pendingArticleURL != nil {
            return false // App was running in background
        }

        // Method 2: Check app launch time vs current time
        // If the app was just backgrounded, the time difference would be small
        let now = Date()
        let timeSinceAppStart = now.timeIntervalSince(appStartTime ?? now)

        // If less than 2 seconds since app start, likely launched from terminated state
        return timeSinceAppStart < 2.0
    }

    // MARK: - Testing Helper (Remove in production)
    #if DEBUG
    func testArticleNotification(url: String) {
        let testUserInfo: [AnyHashable: Any] = [
            "aps": [
                "alert": "Test article notification"
            ],
            "extras": [
                "message_type": "ARTICLE",
                "article_url": url
            ]
        ]

        handleArticleLinkNotification(userInfo: testUserInfo)
    }
    #endif
}

// MARK: - Notification Names
extension Notification.Name {
    static let openArticleFromNotification = Notification.Name("openArticleFromNotification")
}
