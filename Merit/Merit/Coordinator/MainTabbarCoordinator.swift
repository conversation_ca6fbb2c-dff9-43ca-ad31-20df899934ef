//
//  MainTabbarCoordinator.swift
//  TradeX
//
//  Created by <PERSON> C<PERSON> on 16/11/2023.
//

import UIKit
import XCoordinator
import Market
import CUIModule
import Wallet
import WealthPlan
import History
import SharedData
import Authentication
import InstrumentSearch
import MarketDashboard
import Storage
import Notification
import Home

enum MainTabbarRoute: Route {
    case home
    case wallet
    case market
    case wealthPlan
    case history

    case placeOrder(instrument: MarketInstrument)

    case marketDashboard(navigation: UINavigationController?,
                         instrument: MarketInstrument)

    case deposit
    case withdraw

    case otpRequest(navigation: UINavigationController,
                    type: OTPScreenType,
                    completion: (_ type: OTPScreenType) -> Void)

    case search(navigation: UINavigationController?)
    
    case notification(navigation: UINavigationController?)
    case messageCenter(navigation: UINavigationController?)

    case historyOrder(status: OrderStatus?)
    case historyDeposit
    case historyWithdraw
    
    case activeAutoOrders
    case queuedOrders
    
    case statementRequest
    case marketList(category: MarketCategory?)

    var tag: Int {
        switch self {
        case .home:
            return 5
        case .wallet:
            return 3
        case .market:
            return 1
        case .wealthPlan:
            return 2
        case .history:
            return 4
        default:
            return 0
        }
    }
    
    var title: String {
        switch self {
        case .home:
            return "key0399".localized()
        case .wallet:
            return "key0396".localized()
        case .market:
            return "key0395".localized()
        case .wealthPlan: 
            return "key0573".localized()
        case .history:
            return "key0398".localized()

        default:
            return ""
        }
    }
    
    var normalIcon: UIImage? {
        switch self {
        case .home:
            return .image(named: "merit_tabbar_home")
        case .wallet:
            return .image(named: "merit_tabbar_wallet")
        case .market:
            return .image(named: "merit_tabbar_market")
        case .wealthPlan:
            return .image(named: "merit_tabbar_wealth")
        case .history:
            return .image(named: "merit_tabbar_history")

        default:
            return nil
        }
    }

    var selectedIcon: UIImage? {
        switch self {
        case .home:
            return .image(named: "merit_tabbar_home_selected")
        case .wallet:
            return .image(named: "merit_tabbar_wallet_selected")
        case .market:
            return .image(named: "merit_tabbar_market_selected")
        case .wealthPlan:
            return .image(named: "merit_tabbar_wealth_selected")
        case .history:
            return .image(named: "merit_tabbar_history_selected")
            
        default:
            return nil
        }
    }
    
    var tabbarItem: UITabBarItem {
        switch self {
        case .wealthPlan:
            return ESTabBarItem(LargeTabbarItemContentView(),
                                title: title,
                                image: normalIcon,
                                selectedImage: selectedIcon,
                                tag: tag)
            
        default:
            return ESTabBarItem(TabbarItemContentView(),
                                title: title,
                                image: normalIcon,
                                selectedImage: selectedIcon,
                                tag: tag)
        }
    }
}

// MARK: - MainTabbarCoordinator
class MainTabbarCoordinator: TabBarCoordinator<MainTabbarRoute> {

    // MARK: Stored properties
    private let mainTabbarController: MainTabbarController
    private let homeRouter: StrongRouter<HomeRoute>
    private let walletRouter: StrongRouter<WalletRoute>
    private let marketRouter: StrongRouter<MarketRoute>
    private let wealthPlanRouter: StrongRouter<WealthPlanRoute>
    private let historyRouter: StrongRouter<HistoryRoute>

    let homeCoordinator: HomeCoordinator
    let walletCoordinator: WalletCoordinator
    let marketCoordinator: MarketCoordinator
    let wealthPlanCoordinator: WealthPlanCoordinator
    let historyCoordinator: HistoryCoordinator

    var openSettings: (() -> Void)?
    var onboarding: ((_ accountType: UserAccountType) -> Void)?
    var routeToTradeAccount: ((_ route: TradeAccountRoute,
        _ messageId: Int?,
        _ navigation: UINavigationController?,
        _ startViewController: UIViewController?) -> Void)?

    var searchCoordinator: InstrumentSearchCoordinator?
    var marketDashboardCoordinator: MarketDashboardCoordinator?
    var notificationCoordinator: NotificationCoordinator?

    deinit {
        removeThemeChangedObserver()
        NotificationCenter.default.removeObserver(self, name: .openArticleFromNotification, object: nil)
    }
    
    init(openSettings: @escaping (() -> Void),
         onboarding: @escaping ((_ accountType: UserAccountType) -> Void),
         routeToTradeAccount: @escaping ((_ route: TradeAccountRoute,
                                          _ messageId: Int?,
                                          _ navigation: UINavigationController?,
                                          _ startViewController: UIViewController?) -> Void)) {
        homeCoordinator = HomeCoordinator(openSettings: openSettings, 
                                          onboarding: onboarding)
        homeCoordinator.rootViewController.tabBarItem = MainTabbarRoute.home.tabbarItem
        
        walletCoordinator = WalletCoordinator(openSettings: openSettings)
        walletCoordinator.rootViewController.tabBarItem = MainTabbarRoute.wallet.tabbarItem
        
        marketCoordinator = MarketCoordinator(openSettings: openSettings,
                                              onboarding: onboarding)
        marketCoordinator.rootViewController.tabBarItem = MainTabbarRoute.market.tabbarItem
        
        wealthPlanCoordinator = WealthPlanCoordinator(openSettings: openSettings)
        wealthPlanCoordinator.rootViewController.tabBarItem = MainTabbarRoute.wealthPlan.tabbarItem
        
        historyCoordinator = HistoryCoordinator(openSettings: openSettings)
        historyCoordinator.rootViewController.tabBarItem = MainTabbarRoute.history.tabbarItem
        
        mainTabbarController = MainTabbarController(onboarding: onboarding,
                                                    routeToTradeAccount: routeToTradeAccount)
        homeRouter = homeCoordinator.strongRouter
        walletRouter = walletCoordinator.strongRouter
        marketRouter = marketCoordinator.strongRouter
        wealthPlanRouter = wealthPlanCoordinator.strongRouter
        historyRouter = historyCoordinator.strongRouter
        
        super.init(rootViewController: mainTabbarController,
                   tabs: [homeRouter, marketRouter, wealthPlanRouter, walletRouter, historyRouter],
                   select: homeRouter)
        
        self.openSettings = openSettings
        self.onboarding = onboarding
        self.routeToTradeAccount = routeToTradeAccount
        self.configTabbarAppearance()

        homeCoordinator.openSubRoute = commonSubRoutes
        marketCoordinator.openSubRoute = commonSubRoutes
        walletCoordinator.openSubRoute = commonSubRoutes
        wealthPlanCoordinator.openSubRoute = commonSubRoutes
        historyCoordinator.openSubRoute = commonSubRoutes

        observerThemeChanged()
        observeArticleNotifications()
    }
    
    override func prepareTransition(for route: MainTabbarRoute) -> TabBarTransition {
        switch route {
        case .home:
            return .select(homeRouter)
        case .wallet:
            return .select(walletRouter)
        case .market:
            return .select(marketRouter)
        case .wealthPlan:
            return .select(wealthPlanRouter)
        case .history:
            return .select(historyRouter)

        case .placeOrder(_):
            return .none()

        case .marketDashboard(let navigation, let instrument):
            marketDashboardRoute(root: navigation, data: instrument)
            return .none()

        case .deposit:
            return deepLink(MainTabbarRoute.wallet, WalletRoute.overviewAction(.deposit))
            
        case .withdraw:
            return deepLink(MainTabbarRoute.wallet, WalletRoute.overviewAction(.withdraw))

        case .otpRequest(let navigation, let type, let completion):
            otpAuthenticationRoute(root: navigation, type: type, completion: completion)
            return .none()
            
        case .search(let navigation):
            searchRoute(root: navigation)
            return .none()

        case .notification(let navigation):
            notificationRoute(root: navigation, route: .dashboard(.notification))
            return .none()

        case .messageCenter(navigation: let navigation):
            notificationRoute(root: navigation, route: .dashboard(.message))
            return .none()

        case .historyOrder(_):
            HistoryCoordinator.onSelectedCategory.onNext(.order)
            
            return .select(historyRouter)
            
        case .historyDeposit:
            HistoryCoordinator.onSelectedCategory.onNext(.deposit)
            
            return .select(historyRouter)
            
        case .historyWithdraw:
            HistoryCoordinator.onSelectedCategory.onNext(.withdraw)
            
            return .select(historyRouter)
            
        case .activeAutoOrders:
            return .none()

        case .queuedOrders:
            return .none()
            
        case .statementRequest:
            return deepLink(MainTabbarRoute.wallet, WalletRoute.reportGeneration)
            
        case .marketList(let category):
            return deepLink(MainTabbarRoute.market, MarketRoute.marketList(category: category))
        }
    }

    private func commonSubRoutes(_ route: CommonSubRoute) {
        switch route {
        case .search(let navigation):
            self.trigger(.search(navigation: navigation))
            
        case .marketDashboard(let navigation, let data):
            self.trigger(.marketDashboard(navigation: navigation, instrument: data))
            
        case .placeOrder(let data):
            self.trigger(.placeOrder(instrument: data))
            
        case .otpRequest(navigation: let navigation, type: let type, completion: let completion):
            self.trigger(.otpRequest(navigation: navigation, type: type, completion: completion))
            
        case .marketList:
            self.trigger(.market)
            
        case .portfolio:
            guard validateAccountStatus() else { return }
            
            trigger(.wallet)
            
        case .orderHistory(let additionalData):
            HistoryCoordinator.shouldRefreshMyOrders = true
            HistoryCoordinator.myOrdersAdditionalData = additionalData
            self.trigger(.history)
            
        case .walletAction(let action):
            switch action {
            case .deposit: 
                self.trigger(.deposit)
            case .withdraw:
                self.trigger(.withdraw)
            }
            
        case .notification(let navigation):
            self.trigger(.notification(navigation: navigation))
            
        case .messageCenter(navigation: let navigation):
            self.trigger(.messageCenter(navigation: navigation))
            
        case .historyOrder(let status):
            guard validateAccountStatus() else { return }
            
            self.trigger(.historyOrder(status: status))
            
        case .historyDeposit:
            guard validateAccountStatus() else { return }
            
            self.trigger(.historyDeposit)
            
        case .historyWithdraw:
            guard validateAccountStatus() else { return }
            
            self.trigger(.historyWithdraw)
            
        case .activeAutoOrders:
            self.trigger(.activeAutoOrders)
            
        case .queuedOrders:
            self.trigger(.queuedOrders)

        case .retakeSuitTest(let messageId, let navigation, let startViewController):
            routeToTradeAccount?(.retakeSuitTest, messageId, navigation, startViewController)

        case .uploadDocuments(let messageId, let navigation, let startViewController):
            routeToTradeAccount?(.requestMoreDocuments, messageId, navigation, startViewController)

        case .openTradeAccount(let navigation, let startViewController):
            routeToTradeAccount?(.openNewAccount, nil, navigation, startViewController)
            
        case .statementRequest:
            trigger(.statementRequest)
            
        case .favoriteList:
            trigger(.marketList(category: .favorites))
        }
    }
}

// MARK: - Sub Coordinators
private extension MainTabbarCoordinator {
    
    func otpAuthenticationRoute(root: UINavigationController?,
                                type: OTPScreenType,
                                completion: ((_ type: OTPScreenType) -> Void)?) {
        let authenticationCoordinator = AuthenticationCoordinator(root: root)
        authenticationCoordinator.trigger(.otp(type: type,
                                               otpResponse: nil,
                                               completion: completion))
    }
    
    func searchRoute(root: UINavigationController?) {
        searchCoordinator = InstrumentSearchCoordinator(
            root: root,
            subRoute: { [weak self] in
                switch $0 {
                case .marketDashboard(data: let data):
                    self?.trigger(.marketDashboard(navigation: root, instrument: data))
                case .onboarding(let accountType):
                    self?.onboarding?(accountType)
                }
            }
        )
        searchCoordinator?.trigger(.search)
    }
    
    func marketDashboardRoute(root: UINavigationController?,
                              data: MarketInstrument) {
        marketDashboardCoordinator = MarketDashboardCoordinator(
            root: root,
            subRoute: { [weak self] in
                switch $0 {
                case .appSetting:
                    self?.openSettings?()
                case .search(let navigation):
                    if let searchCoordinator = self?.searchCoordinator,
                       searchCoordinator.rootViewController == navigation {
                        searchCoordinator.trigger(.search)
                        return
                    }
                    self?.trigger(.search(navigation: navigation))
                case .placeOrder(let instrument):
                    self?.trigger(.placeOrder(instrument: instrument))
                case .onboarding(let accountType):
                    self?.onboarding?(accountType)
                case .notification(let navigation):
                    self?.trigger(.notification(navigation: navigation))
                case .tradeAccount(let route):
                    self?.routeToTradeAccount?(route, nil, nil, nil)
                }
            }
        )
        marketDashboardCoordinator?.trigger(.marketDashboard(data: data))
    }
    
    func notificationRoute(root: UINavigationController?, route: NotificationRoute) {
        notificationCoordinator = NotificationCoordinator(root: root)
        notificationCoordinator?.trigger(route)
        notificationCoordinator?.openSubRoute = commonSubRoutes
    }
    
    func validateAccountStatus() -> Bool {
        guard
            let accountStatus = AccountStatus(rawValue: Keychain.userInformation?.accountStatus ?? "")
        else { return true }
        
        switch accountStatus {
        case .normal,
                .approved:
            return true
            
        default:
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                self.mainTabbarController.showActionBlockBottomSheet(accountStatus: accountStatus)
            }
            return false
        }
    }
}

// MARK: - Config Tabbar
extension MainTabbarCoordinator {
    
    func configTabbarAppearance() {
        if #available(iOS 15.0, *) {
            let appearance = UITabBarAppearance()
            appearance.configureWithOpaqueBackground()
            appearance.backgroundColor = Color.bgDefault
            
            appearance.shadowColor = .clear
            
            UITabBar.appearance().standardAppearance = appearance
            UITabBar.appearance().scrollEdgeAppearance = appearance
        } else {
            UITabBar.appearance().barTintColor = Color.bgDefault
            UITabBar.appearance().isTranslucent = false
        }
        
        UITabBar.appearance().tintColor = Color.txtTitle
    }

    func observeArticleNotifications() {
        NotificationCenter.default.addObserver(
            forName: .openArticleFromNotification,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self,
                  let userInfo = notification.userInfo,
                  let articleURL = userInfo["articleURL"] as? String else {
                return
            }

            self.handleArticleNotification(url: articleURL)
        }
    }

    private func handleArticleNotification(url: String) {
        // Switch to Home tab first
        trigger(.home)

        // Small delay to ensure tab switch is complete
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            // Navigate to webview with article
            self?.homeCoordinator.trigger(.webview(title: "Article", urlString: url))

            // Additional safety: Ensure badge count is properly synced after navigation
            // This handles edge cases where the badge might not have been decremented properly
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                BadgeCountManager.shared.syncBadgeCountWithLocalState()
            }
        }
    }
}
