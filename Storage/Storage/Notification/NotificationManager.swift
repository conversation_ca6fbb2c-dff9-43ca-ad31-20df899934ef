//
//  NotificationManager.swift
//  Storage
//
//  Created by <PERSON><PERSON> on 2/15/67.
//

import Utils
import RxSwift
import UIKit

public class BadgeCountManager {
    public static let shared = BadgeCountManager()

    private init() {}

    /// Get current app icon badge count
    public var currentBadgeCount: Int {
        return UIApplication.shared.applicationIconBadgeNumber
    }

    /// Set app icon badge count
    public func setBadgeCount(_ count: Int) {
        DispatchQueue.main.async {
            UIApplication.shared.applicationIconBadgeNumber = max(0, count)
        }
    }

    /// Increment badge count by 1
    public func incrementBadgeCount() {
        DispatchQueue.main.async {
            UIApplication.shared.applicationIconBadgeNumber += 1
        }
    }

    /// Decrement badge count by 1
    public func decrementBadgeCount() {
        DispatchQueue.main.async {
            let currentCount = UIApplication.shared.applicationIconBadgeNumber
            UIApplication.shared.applicationIconBadgeNumber = max(0, currentCount - 1)
        }
    }

    /// Clear all badge count
    public func clearBadgeCount() {
        setBadgeCount(0)
    }

    /// Sync badge count with local notification state
    public func syncBadgeCountWithLocalState() {
        let unreadNotiCount = LocalPreference.notifications?.filter { !$0.isRead && !$0.isDeleted }.count ?? 0
        let unreadMessageCount = LocalPreference.unreadMessageCount ?? 0
        let totalUnreadCount = unreadNotiCount + unreadMessageCount

        setBadgeCount(totalUnreadCount)
    }
}

public class NotificationManager {
    public static let shared = NotificationManager()
    
    // Update unread notification
    public let onUpdateUnreadNotification = BehaviorSubject<Bool>(value: false)

    public static let observerName = Notification.Name("UNREADNOTIFICATION")

    public static var orderEndDate: Int {
        Int((Date().timeIntervalSince1970 + 60) * 1000) // To miliseconds
    }

    public static var orderStartDate: Int {
        let startDate = Date().adding(component: .day, value: -7)
        return Int(startDate.timeIntervalSince1970 * 1000)
    }

    private var newOrderNotiReceived: Bool = false
    private var newTransactionNotiReceived: Bool = false

    public func replace(notifications: [NotificationModel]) {
        LocalPreference.notifications = notifications
    }

    public func add(notification: NotificationModel) {
        var notifications = LocalPreference.notifications ?? []
        notifications.append(notification)
        LocalPreference.notifications = notifications

        // Send new noti received
        checkUnreadNotification()
    }

    public func setRead(notiId: Int, isRead: Bool) {
        var notifications = LocalPreference.notifications ?? []

        if let index = notifications.firstIndex(where: { $0.id == notiId }) {
            notifications[index].isRead = isRead
        }

        LocalPreference.notifications = notifications

        // Update badge count when notification read state changes
        checkUnreadNotification()
    }

    public func setAllRead(isRead: Bool) {
        var notifications = LocalPreference.notifications ?? []

        for index in 0..<notifications.count {
            notifications[index].isRead = isRead
        }

        LocalPreference.notifications = notifications

        // Update badge count when notification read state changes
        checkUnreadNotification()
    }

    public func setDelete(notiId: Int, isDeleted: Bool) {
        var notifications = LocalPreference.notifications ?? []

        if let index = notifications.firstIndex(where: { $0.id == notiId }) {
            notifications[index].isDeleted = isDeleted
        }

        LocalPreference.notifications = notifications
    }

    public func setAllDelete(isDeleted: Bool) {
        var notifications = LocalPreference.notifications ?? []

        for index in 0..<notifications.count {
            notifications[index].isDeleted = isDeleted
        }

        LocalPreference.notifications = notifications
    }

    public func checkUnreadNotification() {
        let unreadNotiCount = LocalPreference.notifications?.filter { !$0.isRead && !$0.isDeleted }.count ?? 0
        let unreadMessageCount = LocalPreference.unreadMessageCount ?? 0

        NotificationCenter.default.post(
            name: NotificationManager.observerName,
            object: nil,
            userInfo: ["unreadCount": unreadNotiCount + unreadMessageCount]
        )

        // Sync app icon badge count with local notification state
        BadgeCountManager.shared.syncBadgeCountWithLocalState()
    }
}
